# Flight Landing Pages - Page Types Implementation Plan

## Overview

Transform the current content element-based system into a proper page type system with template pages and dynamic landing pages.

## Architecture Changes

### Current System
```
Regular Page → Flight Landing Page Plugin → Virtual Route Middleware → Dynamic Content
```

### Proposed System
```
Template Page (doktype=200) → Landing Page (doktype=201) → Virtual Route Middleware → Dynamic Page Generation
```

## Implementation Steps

### 1. Create New Page Types

#### A. Template Page Type (doktype=200)
- **Purpose**: Store content elements and layout that serve as templates
- **Characteristics**:
  - Not directly accessible via frontend
  - Contains normal content elements with placeholders
  - Can be organized in storage folders
  - Backend editing like normal pages

#### B. Landing Page Type (doktype=201)  
- **Purpose**: Define URL structure and reference template pages
- **Characteristics**:
  - Contains URL pattern with placeholders (e.g., `/flights/{origin}-{destination}`)
  - References template page via TSconfig or database field
  - Manages flight route records
  - Generates virtual routes

### 2. Database Schema Changes

#### A. Extend pages table
```sql
ALTER TABLE pages ADD COLUMN tx_flightlandingpages_template_page int(11) DEFAULT 0;
ALTER TABLE pages ADD COLUMN tx_flightlandingpages_url_pattern varchar(255) DEFAULT '';
ALTER TABLE pages ADD COLUMN tx_flightlandingpages_route_prefix varchar(100) DEFAULT '';
```

#### B. Extend flight route table
```sql
ALTER TABLE tx_flightlandingpages_domain_model_flightroute 
ADD COLUMN landing_page_uid int(11) DEFAULT 0;
```

### 3. TCA Configuration

#### A. Page Types Registration
```php
// Configuration/TCA/Overrides/pages.php
$GLOBALS['TCA']['pages']['ctrl']['typeicon_classes'][200] = 'apps-pagetree-flight-template';
$GLOBALS['TCA']['pages']['ctrl']['typeicon_classes'][201] = 'apps-pagetree-flight-landing';

// Add new page types to doktype select
$GLOBALS['TCA']['pages']['columns']['doktype']['config']['items'][] = [
    'LLL:EXT:flight_landing_pages/Resources/Private/Language/locallang_db.xlf:pages.doktype.200',
    200,
    'apps-pagetree-flight-template'
];
```

#### B. Template Page Configuration
```php
// Template pages - normal content editing
$GLOBALS['TCA']['pages']['types'][200] = [
    'showitem' => '
        --div--;LLL:EXT:core/Resources/Private/Language/Form/locallang_tabs.xlf:general,
            doktype,
            title,
            slug,
        --div--;LLL:EXT:core/Resources/Private/Language/Form/locallang_tabs.xlf:access,
            hidden,
            starttime,
            endtime
    '
];
```

#### C. Landing Page Configuration  
```php
// Landing pages - route configuration
$GLOBALS['TCA']['pages']['types'][201] = [
    'showitem' => '
        --div--;LLL:EXT:core/Resources/Private/Language/Form/locallang_tabs.xlf:general,
            doktype,
            title,
            slug,
            tx_flightlandingpages_template_page,
            tx_flightlandingpages_url_pattern,
        --div--;LLL:EXT:flight_landing_pages/Resources/Private/Language/locallang_db.xlf:pages.tabs.routes,
            tx_flightlandingpages_routes
    '
];
```

### 4. Enhanced Middleware

#### A. Page Type Detection
```php
class VirtualRouteMiddleware implements MiddlewareInterface
{
    public function process(ServerRequestInterface $request, RequestHandlerInterface $handler): ResponseInterface
    {
        $path = $request->getUri()->getPath();
        $site = $request->getAttribute('site');
        
        // Find landing pages for this site
        $landingPages = $this->findLandingPagesForSite($site->getIdentifier());
        
        foreach ($landingPages as $landingPage) {
            if ($this->matchesUrlPattern($path, $landingPage)) {
                return $this->generateDynamicPage($request, $landingPage, $path);
            }
        }
        
        return $handler->handle($request);
    }
}
```

#### B. Dynamic Page Generation
```php
protected function generateDynamicPage(ServerRequestInterface $request, array $landingPage, string $path): ResponseInterface
{
    // Extract route parameters from URL
    $routeParams = $this->extractRouteParams($path, $landingPage['url_pattern']);
    
    // Get flight route data
    $flightRoute = $this->getFlightRoute($routeParams, $landingPage['uid']);
    
    // Get template page content
    $templatePage = $this->getTemplatePage($landingPage['template_page']);
    
    // Generate dynamic page
    $dynamicPage = $this->pageGenerationService->generatePage(
        $templatePage,
        $flightRoute,
        $routeParams
    );
    
    // Set page data in request
    $request = $request->withAttribute('dynamicPage', $dynamicPage);
    $request = $request->withAttribute('flightRoute', $flightRoute);
    
    return $this->renderDynamicPage($request, $dynamicPage);
}
```

### 5. Page Generation Service

#### A. Template Processing
```php
class PageGenerationService
{
    public function generatePage(array $templatePage, FlightRoute $flightRoute, array $routeParams): array
    {
        // Get all content elements from template page
        $contentElements = $this->getContentElements($templatePage['uid']);
        
        // Process each content element
        $processedElements = [];
        foreach ($contentElements as $element) {
            $processedElements[] = $this->processContentElement($element, $flightRoute, $routeParams);
        }
        
        // Generate page structure
        return [
            'uid' => 'virtual_' . $flightRoute->getUid(),
            'title' => $this->replacePlaceholders($templatePage['title'], $flightRoute, $routeParams),
            'content_elements' => $processedElements,
            'meta' => $this->generateMetaData($flightRoute, $routeParams)
        ];
    }
}
```

### 6. TSconfig Integration

#### A. Template Page Reference
```typoscript
# Page TSconfig for landing pages
TCEFORM.pages.tx_flightlandingpages_template_page {
    PAGE_TSCONFIG_ID = 123
    addItems {
        123 = Template: Flight Search Results
        124 = Template: Flight Comparison
    }
}
```

#### B. Configuration Options
```typoscript
# Site configuration
plugin.tx_flightlandingpages {
    settings {
        defaultTemplatePage = 123
        enableCaching = 1
        cacheLifetime = 3600
    }
}
```

## Benefits of This Approach

### 1. **Better Content Management**
- Template pages can be edited like normal pages
- Content editors can use all TYPO3 content elements
- Visual page builder compatibility
- Better content preview

### 2. **Improved Performance**
- Page-level caching possible
- Better integration with TYPO3 caching
- Reduced database queries

### 3. **Enhanced SEO**
- Proper page titles and meta tags
- Better URL structure
- Improved sitemap generation

### 4. **Developer Experience**
- Cleaner architecture
- Better separation of concerns
- Easier testing and debugging
- Standard TYPO3 patterns

## Migration Strategy

### Phase 1: Implement New Page Types
- Add page types and TCA
- Create backend interface
- Basic page generation

### Phase 2: Enhanced Middleware
- Modify existing middleware
- Add page generation service
- Template processing

### Phase 3: Migration Tools
- Convert existing content elements to template pages
- Migrate route configurations
- Update documentation

### Phase 4: Cleanup
- Remove old content element approach
- Optimize performance
- Add advanced features

## Estimated Effort

- **Small Changes**: TSconfig integration, basic TCA (1-2 days)
- **Medium Changes**: Page types, middleware updates (3-5 days)  
- **Large Changes**: Page generation service, template processing (5-7 days)
- **Testing & Migration**: (2-3 days)

**Total Estimated Effort: 11-17 days**

This is a significant but very worthwhile improvement that would make the extension much more powerful and user-friendly.
