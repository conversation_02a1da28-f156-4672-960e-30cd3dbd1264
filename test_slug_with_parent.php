<?php

/**
 * Test script to verify slug generation with parent page path
 */

require_once __DIR__ . '/packages/flight_landing_pages/Classes/UserFunctions/SlugModifier.php';

use Bgs\FlightLandingPages\UserFunctions\SlugModifier;

// Test data based on actual database records
$testData = [
    [
        'origin_code' => 'BER',
        'destination_code' => 'SOF',
        'pid' => 3, // flights page
        'expected_base_slug' => 'ber-to-sof',
        'expected_final_slug' => 'flights/from-ber-to-sof'
    ],
    [
        'origin_code' => 'FRA',
        'destination_code' => 'SOF',
        'pid' => 5, // landing page under flights
        'expected_base_slug' => 'fra-to-sof',
        'expected_final_slug' => 'flights/default-6dc0e212da9dd2dc8f64a8fdcde3e8e1/from-fra-to-sof'
    ],
];

echo "Testing Flight Route Slug Generation with Parent Page Path\n";
echo "=========================================================\n\n";

$slugModifier = new SlugModifier();

foreach ($testData as $index => $data) {
    echo "Test " . ($index + 1) . ":\n";
    echo "Origin: {$data['origin_code']}, Destination: {$data['destination_code']}, PID: {$data['pid']}\n";
    
    // Simulate the slug that would be generated by TYPO3's slug field
    $generatedSlug = strtolower($data['origin_code']) . '-to-' . strtolower($data['destination_code']);
    
    echo "Base generated slug: {$generatedSlug}\n";
    
    // Test the postModifier with parent page path
    $parameters = [
        'slug' => $generatedSlug,
        'pid' => $data['pid'],
        'record' => ['pid' => $data['pid']]
    ];
    $finalSlug = $slugModifier->addFromPrefix($parameters);
    
    echo "Final slug with parent path: {$finalSlug}\n";
    echo "Expected: {$data['expected_final_slug']}\n";
    
    // Verify result
    $success = ($finalSlug === $data['expected_final_slug']) ? "✓ PASS" : "✗ FAIL";
    echo "Result: {$success}\n";
    echo str_repeat('-', 60) . "\n\n";
}

echo "Test completed!\n";
