<?php
namespace Bgs\FlightLandingPages\Tests\Unit\Service;

use PHPUnit\Framework\TestCase;
use TYPO3\CMS\Core\Site\Entity\Site;
use TYPO3\CMS\Core\Site\SiteFinder;
use TYPO3\TestingFramework\Core\Unit\UnitTestCase;
use Bgs\FlightLandingPages\Service\UrlGenerationService;
use Bgs\FlightLandingPages\Domain\Model\FlightRoute;
use Bgs\FlightLandingPages\Domain\Model\LandingPage;

/**
 * Test case for UrlGenerationService
 */
class UrlGenerationServiceTest extends UnitTestCase
{
    protected UrlGenerationService $urlGenerationService;
    protected SiteFinder $siteFinderMock;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->siteFinderMock = $this->createMock(SiteFinder::class);
        $this->urlGenerationService = new UrlGenerationService($this->siteFinderMock);
    }

    /**
     * @test
     */
    public function buildUrlCombinesComponentsCorrectly(): void
    {
        $baseUrl = 'https://example.com';
        $landingPagePath = 'flights';
        $routeSlug = 'from-BER-to-SOF';

        $result = $this->callProtectedMethod(
            $this->urlGenerationService,
            'buildUrl',
            [$baseUrl, $landingPagePath, $routeSlug]
        );

        $expected = 'https://example.com/flights/from-BER-to-SOF';
        self::assertEquals($expected, $result);
    }

    /**
     * @test
     */
    public function buildUrlHandlesEmptyLandingPagePath(): void
    {
        $baseUrl = 'https://example.com';
        $landingPagePath = '';
        $routeSlug = 'from-BER-to-SOF';

        $result = $this->callProtectedMethod(
            $this->urlGenerationService,
            'buildUrl',
            [$baseUrl, $landingPagePath, $routeSlug]
        );

        $expected = 'https://example.com/from-BER-to-SOF';
        self::assertEquals($expected, $result);
    }

    /**
     * @test
     */
    public function buildUrlHandlesTrailingSlashes(): void
    {
        $baseUrl = 'https://example.com/';
        $landingPagePath = '/flights/';
        $routeSlug = '/from-BER-to-SOF/';

        $result = $this->callProtectedMethod(
            $this->urlGenerationService,
            'buildUrl',
            [$baseUrl, $landingPagePath, $routeSlug]
        );

        $expected = 'https://example.com/flights/from-BER-to-SOF';
        self::assertEquals($expected, $result);
    }

    /**
     * @test
     */
    public function validateUrlComponentsReturnsTrueForValidComponents(): void
    {
        $baseUrl = 'https://example.com';
        $landingPagePath = 'flights';
        $routeSlug = 'from-BER-to-SOF';

        $result = $this->urlGenerationService->validateUrlComponents($baseUrl, $landingPagePath, $routeSlug);

        self::assertTrue($result);
    }

    /**
     * @test
     */
    public function validateUrlComponentsReturnsFalseForEmptyBaseUrl(): void
    {
        $baseUrl = '';
        $landingPagePath = 'flights';
        $routeSlug = 'from-BER-to-SOF';

        $result = $this->urlGenerationService->validateUrlComponents($baseUrl, $landingPagePath, $routeSlug);

        self::assertFalse($result);
    }

    /**
     * @test
     */
    public function validateUrlComponentsReturnsFalseForEmptyRouteSlug(): void
    {
        $baseUrl = 'https://example.com';
        $landingPagePath = 'flights';
        $routeSlug = '';

        $result = $this->urlGenerationService->validateUrlComponents($baseUrl, $landingPagePath, $routeSlug);

        self::assertFalse($result);
    }

    /**
     * @test
     */
    public function parseUrlExtractsComponentsCorrectly(): void
    {
        $url = 'https://example.com/flights/from-BER-to-SOF';
        $baseUrl = 'https://example.com';

        $result = $this->urlGenerationService->parseUrl($url, $baseUrl);

        $expected = [
            'landingPagePath' => 'flights',
            'routeSlug' => 'from-BER-to-SOF'
        ];

        self::assertEquals($expected, $result);
    }

    /**
     * @test
     */
    public function parseUrlHandlesUrlWithoutLandingPagePath(): void
    {
        $url = 'https://example.com/from-BER-to-SOF';
        $baseUrl = 'https://example.com';

        $result = $this->urlGenerationService->parseUrl($url, $baseUrl);

        $expected = [
            'landingPagePath' => '',
            'routeSlug' => 'from-BER-to-SOF'
        ];

        self::assertEquals($expected, $result);
    }

    /**
     * Helper method to call protected methods
     */
    protected function callProtectedMethod($object, $methodName, array $parameters = [])
    {
        $reflection = new \ReflectionClass(get_class($object));
        $method = $reflection->getMethod($methodName);
        $method->setAccessible(true);

        return $method->invokeArgs($object, $parameters);
    }
}
