/**
 * Flight Landing Pages Extension JavaScript
 */

document.addEventListener('DOMContentLoaded', function() {

    // Initialize flight reference filters
    initializeFlightFilters();



});

/**
 * Initialize flight reference list filters
 */
function initializeFlightFilters() {
    const originFilter = document.getElementById('origin-filter');
    const destinationFilter = document.getElementById('destination-filter');
    const searchInput = document.getElementById('route-search');
    const clearButton = document.getElementById('clear-search');

    // Check if we have any filters on this page
    if (!originFilter && !destinationFilter && !searchInput) {
        return; // No filters on this page
    }

    // Get all route items
    const routeItems = document.querySelectorAll('[data-origin][data-destination]');

    function filterRoutes() {
        const selectedOrigin = originFilter ? originFilter.value : '';
        const selectedDestination = destinationFilter ? destinationFilter.value : '';
        const searchTerm = searchInput ? searchInput.value.toLowerCase().trim() : '';

        // Show/hide clear button based on search input
        if (clearButton && searchInput) {
            clearButton.style.display = searchTerm ? 'block' : 'none';
        }

        routeItems.forEach(function(item) {
            const itemOrigin = item.getAttribute('data-origin');
            const itemDestination = item.getAttribute('data-destination');
            const searchContent = item.getAttribute('data-search-content') || '';

            // Check dropdown filters
            const originMatch = !selectedOrigin || itemOrigin === selectedOrigin;
            const destinationMatch = !selectedDestination || itemDestination === selectedDestination;

            // Check text search
            const searchMatch = !searchTerm || searchContent.toLowerCase().includes(searchTerm);

            if (originMatch && destinationMatch && searchMatch) {
                item.style.display = '';
            } else {
                item.style.display = 'none';
            }
        });

        // Update results count
        updateResultsCount();
    }

    function updateResultsCount() {
        const visibleItems = Array.from(routeItems).filter(item => item.style.display !== 'none');
        const countElement = document.querySelector('.results-count');

        if (countElement) {
            if (visibleItems.length === routeItems.length) {
                countElement.textContent = `Showing all ${routeItems.length} routes`;
            } else {
                countElement.textContent = `Showing ${visibleItems.length} of ${routeItems.length} routes`;
            }
        }
    }

    function clearSearch() {
        if (searchInput) {
            searchInput.value = '';
            searchInput.focus();
            filterRoutes();
        }
    }

    // Add event listeners
    if (originFilter) {
        originFilter.addEventListener('change', filterRoutes);
    }

    if (destinationFilter) {
        destinationFilter.addEventListener('change', filterRoutes);
    }

    if (searchInput) {
        // Use input event for real-time search
        searchInput.addEventListener('input', filterRoutes);

        // Also listen for paste events
        searchInput.addEventListener('paste', function() {
            // Small delay to allow paste to complete
            setTimeout(filterRoutes, 10);
        });

        // Handle keyboard shortcuts
        searchInput.addEventListener('keydown', function(e) {
            // Clear search on Escape key
            if (e.key === 'Escape') {
                clearSearch();
            }
        });
    }

    if (clearButton) {
        clearButton.addEventListener('click', clearSearch);
    }

    // Initialize results count
    updateResultsCount();
}



/**
 * Utility function to show/hide elements with animation
 */
function toggleElement(element, show) {
    if (show) {
        element.style.display = '';
        element.style.opacity = '0';
        element.style.transition = 'opacity 0.3s ease';

        setTimeout(function() {
            element.style.opacity = '1';
        }, 10);
    } else {
        element.style.transition = 'opacity 0.3s ease';
        element.style.opacity = '0';

        setTimeout(function() {
            element.style.display = 'none';
        }, 300);
    }
}

/**
 * Add smooth scrolling to anchor links
 */
document.querySelectorAll('a[href^="#"]').forEach(function(anchor) {
    anchor.addEventListener('click', function(e) {
        e.preventDefault();

        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

/**
 * Add click tracking for flight route links (for analytics)
 */
document.querySelectorAll('.route-card a, .route-item a').forEach(function(link) {
    link.addEventListener('click', function() {
        // Track click event (integrate with your analytics solution)
        if (typeof gtag !== 'undefined') {
            gtag('event', 'click', {
                'event_category': 'Flight Route',
                'event_label': this.href
            });
        }

        // You can also send data to other analytics services here
        console.log('Flight route clicked:', this.href);
    });
});
