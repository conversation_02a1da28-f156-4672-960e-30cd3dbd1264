/**
 * Flight Landing Pages Backend JavaScript
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize backend search filter
    initializeBackendSearch();
});

/**
 * Initialize backend search functionality for destination pairs
 */
function initializeBackendSearch() {
    const searchInput = document.getElementById('backend-route-search');
    const clearButton = document.getElementById('clear-backend-search');
    const resultsCount = document.getElementById('backend-results-count');
    const routeItems = document.querySelectorAll('.flight-route-item[data-search-content]');

    if (!searchInput || routeItems.length === 0) {
        return; // No search functionality needed
    }

    function filterBackendRoutes() {
        const searchTerm = searchInput.value.toLowerCase().trim();
        let visibleCount = 0;

        // Show/hide clear button
        if (clearButton) {
            clearButton.style.display = searchTerm ? 'block' : 'none';
        }

        routeItems.forEach(function(item) {
            const searchContent = item.getAttribute('data-search-content') || '';
            const isMatch = !searchTerm || searchContent.toLowerCase().includes(searchTerm);

            if (isMatch) {
                item.classList.remove('search-hidden');
                visibleCount++;
            } else {
                item.classList.add('search-hidden');
            }
        });

        // Update results count
        if (resultsCount) {
            if (visibleCount === routeItems.length) {
                resultsCount.textContent = `Showing all ${routeItems.length} destination pairs`;
            } else {
                resultsCount.textContent = `Showing ${visibleCount} of ${routeItems.length} destination pairs`;
            }
        }
    }

    function clearBackendSearch() {
        searchInput.value = '';
        searchInput.focus();
        filterBackendRoutes();
    }

    // Event listeners
    searchInput.addEventListener('input', filterBackendRoutes);
    searchInput.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            clearBackendSearch();
        }
    });

    if (clearButton) {
        clearButton.addEventListener('click', clearBackendSearch);
    }

    // Initialize count
    filterBackendRoutes();
}
