<?xml version="1.0" encoding="utf-8" standalone="yes" ?>
<xliff version="1.0">
    <file source-language="en" datatype="plaintext" original="messages" date="2024-01-01T00:00:00Z" product-name="flight_landing_pages">
        <header/>
        <body>
            <!-- Flight Route Model -->
            <trans-unit id="tx_flightlandingpages_domain_model_flightroute">
                <source>Flight Route</source>
            </trans-unit>
            <trans-unit id="tx_flightlandingpages_domain_model_flightroute.origin_code">
                <source>Origin Code</source>
            </trans-unit>
            <trans-unit id="tx_flightlandingpages_domain_model_flightroute.origin_name">
                <source>Origin Name</source>
            </trans-unit>
            <trans-unit id="tx_flightlandingpages_domain_model_flightroute.origin_type">
                <source>Origin Type</source>
            </trans-unit>
            <trans-unit id="tx_flightlandingpages_domain_model_flightroute.origin_type.airport">
                <source>Airport</source>
            </trans-unit>
            <trans-unit id="tx_flightlandingpages_domain_model_flightroute.origin_type.city">
                <source>City</source>
            </trans-unit>
            <trans-unit id="tx_flightlandingpages_domain_model_flightroute.origin_type.country">
                <source>Country</source>
            </trans-unit>
            <trans-unit id="tx_flightlandingpages_domain_model_flightroute.destination_code">
                <source>Destination Code</source>
            </trans-unit>
            <trans-unit id="tx_flightlandingpages_domain_model_flightroute.destination_name">
                <source>Destination Name</source>
            </trans-unit>
            <trans-unit id="tx_flightlandingpages_domain_model_flightroute.destination_type">
                <source>Destination Type</source>
            </trans-unit>
            <trans-unit id="tx_flightlandingpages_domain_model_flightroute.destination_type.airport">
                <source>Airport</source>
            </trans-unit>
            <trans-unit id="tx_flightlandingpages_domain_model_flightroute.destination_type.city">
                <source>City</source>
            </trans-unit>
            <trans-unit id="tx_flightlandingpages_domain_model_flightroute.destination_type.country">
                <source>Country</source>
            </trans-unit>
            <trans-unit id="tx_flightlandingpages_domain_model_flightroute.route_slug">
                <source>Route Slug (URL Segment)</source>
            </trans-unit>
            <trans-unit id="tx_flightlandingpages_domain_model_flightroute.is_active">
                <source>Is Active</source>
            </trans-unit>
            <trans-unit id="tx_flightlandingpages_domain_model_flightroute.origin">
                <source>Origin</source>
            </trans-unit>
            <trans-unit id="tx_flightlandingpages_domain_model_flightroute.destination">
                <source>Destination</source>
            </trans-unit>


            <!-- Page Types -->
            <trans-unit id="pages.doktype.200">
                <source>Flight Template Page</source>
            </trans-unit>
            <trans-unit id="pages.doktype.201">
                <source>Flight Landing Page</source>
            </trans-unit>

            <!-- Page Fields -->
            <trans-unit id="pages.tx_flightlandingpages_template_page">
                <source>Template Page</source>
            </trans-unit>
            <trans-unit id="pages.tx_flightlandingpages_url_pattern">
                <source>URL Pattern</source>
            </trans-unit>
            <trans-unit id="pages.tx_flightlandingpages_route_prefix">
                <source>Route Prefix</source>
            </trans-unit>
            <trans-unit id="pages.tx_flightlandingpages_cache_lifetime">
                <source>Cache Lifetime (seconds)</source>
            </trans-unit>
            <trans-unit id="pages.tx_flightlandingpages_enable_sitemap">
                <source>Enable in Sitemap</source>
            </trans-unit>

            <!-- Tabs -->
            <trans-unit id="pages.tabs.configuration">
                <source>Flight Configuration</source>
            </trans-unit>

            <!-- Plugin Titles -->
            <trans-unit id="plugin.flight_page.title">
                <source>Flight Landing Page</source>
            </trans-unit>

            <trans-unit id="plugin.flight_reference.title">
                <source>Flight Reference List</source>
            </trans-unit>

            <!-- Backend Preview Labels -->
            <trans-unit id="pages.preview.template_page_title">
                <source>Template Page</source>
            </trans-unit>
            <trans-unit id="pages.preview.valid_template_page">
                <source>Valid Flight Template Page</source>
            </trans-unit>
            <trans-unit id="pages.preview.invalid_template_page">
                <source>Warning: Referenced page is not a Flight Template Page (doktype 200)</source>
            </trans-unit>
            <trans-unit id="pages.preview.template_page_hidden">
                <source>Warning: Template page is hidden</source>
            </trans-unit>
            <trans-unit id="pages.preview.destination_pairs_title">
                <source>Available Destination Pairs</source>
            </trans-unit>
            <trans-unit id="pages.preview.no_destination_pairs_title">
                <source>No Destination Pairs</source>
            </trans-unit>
            <trans-unit id="pages.preview.no_destination_pairs_message">
                <source>No flight routes have been configured for this landing page. Add flight route records to enable virtual routes.</source>
            </trans-unit>
            <trans-unit id="pages.preview.add_pairs_button">
                <source>Add Pairs</source>
            </trans-unit>
            <trans-unit id="pages.preview.add_first_pair_button">
                <source>Add First Pair</source>
            </trans-unit>
            <trans-unit id="pages.preview.add_destination_pair">
                <source>Add new destination pair for this landing page</source>
            </trans-unit>
            <trans-unit id="pages.preview.preview_route">
                <source>Preview this flight route in frontend</source>
            </trans-unit>
            <trans-unit id="pages.preview.edit_route">
                <source>Edit this flight route</source>
            </trans-unit>
            <trans-unit id="pages.preview.preview_route_inactive">
                <source>Preview not available for inactive routes</source>
            </trans-unit>
            <trans-unit id="pages.preview.route_inactive">
                <source>Inactive</source>
            </trans-unit>
            <trans-unit id="pages.preview.active_count">
                <source>active</source>
            </trans-unit>
            <trans-unit id="pages.preview.inactive_count">
                <source>inactive</source>
            </trans-unit>
            <trans-unit id="pages.preview.export_csv_button">
                <source>Export CSV</source>
            </trans-unit>
            <trans-unit id="pages.preview.export_csv_title">
                <source>Export destination pairs as CSV file</source>
            </trans-unit>
            <trans-unit id="pages.preview.import_csv_button">
                <source>Import CSV</source>
            </trans-unit>
            <trans-unit id="pages.preview.import_csv_title">
                <source>Import destination pairs from CSV file</source>
            </trans-unit>
            <trans-unit id="pages.import.title">
                <source>Import Destination Pairs</source>
            </trans-unit>
            <trans-unit id="pages.import.description">
                <source>Upload a CSV file to import destination pairs. The CSV should contain columns: Origin Code, Origin Name, Origin Type, Destination Code, Destination Name, Destination Type, Route Slug, Is Active. The CSV file must not contain duplicate routes (same origin type, origin code, destination type, and destination code).</source>
            </trans-unit>
            <trans-unit id="pages.import.file_label">
                <source>CSV File</source>
            </trans-unit>
            <trans-unit id="pages.import.submit_button">
                <source>Import</source>
            </trans-unit>
            <trans-unit id="pages.import.cancel_button">
                <source>Cancel</source>
            </trans-unit>
            <trans-unit id="pages.import.success">
                <source>Successfully imported {0} destination pairs.</source>
            </trans-unit>
            <trans-unit id="pages.import.error">
                <source>Import failed: {0}</source>
            </trans-unit>
        </body>
    </file>
</xliff>
