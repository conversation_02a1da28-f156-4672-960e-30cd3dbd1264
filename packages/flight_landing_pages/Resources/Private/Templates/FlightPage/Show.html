<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers" 
      xmlns:flp="http://typo3.org/ns/Vendor/FlightLandingPages/ViewHelpers" 
      data-namespace-typo3-fluid="true">
<f:layout name="Default" />

<f:section name="Main">
    <div class="flight-page">
        <f:if condition="{isVirtualRoute}">
            <f:then>
                <!-- Virtual Route Display -->
                <div class="flight-route-header">
                    <h1>Flights from {flightRoute.originName} to {flightRoute.destinationName}</h1>
                    <p class="route-info">
                        <span class="origin">{flightRoute.originCode} ({flightRoute.originType})</span>
                        →
                        <span class="destination">{flightRoute.destinationCode} ({flightRoute.destinationType})</span>
                    </p>
                </div>

                <div class="flight-details">
                    <div class="price-info">
                        <span class="price">{flightData.price} {flightData.currency}</span>
                        <span class="airline">{flightData.airline}</span>
                    </div>
                    
                    <div class="flight-times">
                        <div class="departure">
                            <strong>Departure:</strong> {flightData.flight.departureTime}
                        </div>
                        <div class="arrival">
                            <strong>Arrival:</strong> {flightData.flight.arrivalTime}
                        </div>
                        <div class="duration">
                            <strong>Duration:</strong> {flightData.flight.duration}
                        </div>
                    </div>

                    <div class="dates">
                        <div class="departure-date">
                            <strong>Departure Date:</strong> {flightData.departureDate}
                        </div>
                        <div class="return-date">
                            <strong>Return Date:</strong> {flightData.returnDate}
                        </div>
                    </div>
                </div>

                <!-- Process custom template content if available -->
                <f:if condition="{processedContent}">
                    <div class="custom-content">
                        <f:format.raw>{processedContent}</f:format.raw>
                    </div>
                </f:if>
            </f:then>
            <f:else>
                <!-- Regular Flight Page Display -->
                <div class="flight-page-content">
                    <h1>Flight Information</h1>
                    
                    <f:if condition="{flightData}">
                        <div class="flight-info">
                            <h2>Flight {flightData.flight.number}</h2>
                            <p><strong>From:</strong> {flightData.origin}</p>
                            <p><strong>To:</strong> {flightData.destination}</p>
                            <p><strong>Price:</strong> {flightData.price} {flightData.currency}</p>
                            <p><strong>Airline:</strong> {flightData.airline}</p>
                        </div>
                    </f:if>

                    <f:if condition="{processedContent}">
                        <div class="processed-content">
                            <f:format.raw>{processedContent}</f:format.raw>
                        </div>
                    </f:if>
                </div>
            </f:else>
        </f:if>
    </div>
</f:section>
</html>
