# Frontend Testing Guide for Flight Landing Pages

This document explains how to test that Flight Landing Pages render correctly as standard TYPO3 pages in the frontend.

## Overview

Flight Landing Pages (doktype 201) should render normally as standard TYPO3 pages with the following capabilities:

1. **Standard Page Rendering**: Normal content elements, navigation, and page structure
2. **Virtual Route Support**: Dynamic content based on URL parameters (e.g., `/ber-sof`)
3. **Template Page Integration**: Content from template pages with placeholder replacement
4. **Site Integration**: Proper integration with TYPO3 site configuration and routing

## Testing Setup

### 1. Create Test Pages

#### Create a Flight Template Page (doktype 200):
1. Go to TYPO3 backend → Web → Page
2. Create a new page with doktype "Flight Template Page" (200)
3. Title: "Flight Template - Routes"
4. Add content elements with placeholders:
   - **Header**: "Flights from {origin} to {destination}"
   - **Text**: "Find the best flights from {origin} to {destination}. Starting from {price} {currency}."
   - **Text**: "Flight duration: {flight.duration}"

#### Create a Flight Landing Page (doktype 201):
1. Create a new page with doktype "Flight Landing Page" (201)
2. Title: "Flight Routes"
3. Slug: `/flights`
4. In "Template Page" field, select the template page created above
5. Add some normal content elements:
   - **Header**: "Welcome to Flight Search"
   - **Text**: "This is normal page content that should always be visible."

#### Create Flight Route Records:
1. Go to List module
2. Navigate to the Flight Landing Page
3. Create flight route records:
   - **Route 1**: BER → SOF (Berlin → Sofia), slug: `ber-sof`
   - **Route 2**: BER → VIE (Berlin → Vienna), slug: `ber-vie`
   - **Route 3**: MUC → BCN (Munich → Barcelona), slug: `muc-bcn`
4. Set "Landing Page" field to point to your Flight Landing Page
5. Ensure "Is Active" is checked

### 2. Test Standard Page Rendering

#### Test Normal Page Access:
1. Visit the Flight Landing Page directly: `https://yoursite.com/flights`
2. **Expected Result**:
   - Page loads normally with standard TYPO3 page structure
   - Shows page title "Flight Routes"
   - Shows normal content elements you added
   - Navigation menu works correctly
   - No flight-specific content is shown (since no route is specified)

#### Test Page Structure:
1. Check that the page includes:
   - Proper HTML structure with `<html>`, `<head>`, `<body>` tags
   - Meta tags (viewport, charset)
   - Page title in `<title>` tag
   - CSS and JavaScript files are loaded
   - Navigation menu is rendered
   - Footer content is displayed

### 3. Test Virtual Route Rendering

#### Test Virtual Route Access:
1. Visit a virtual route: `https://yoursite.com/flights/ber-sof`
2. **Expected Result**:
   - Page loads with dynamic title: "Flights from Berlin to Sofia"
   - Shows route information: "BER (airport) → SOF (airport)"
   - Template page content is rendered with placeholders replaced
   - Normal page structure is maintained

#### Test Different Routes:
1. Test each route:
   - `https://yoursite.com/flights/ber-vie` → "Flights from Berlin to Vienna"
   - `https://yoursite.com/flights/muc-bcn` → "Flights from Munich to Barcelona"
2. **Expected Result**:
   - Each route shows correct origin/destination information
   - Placeholders are properly replaced with route-specific data
   - Page structure remains consistent

#### Test Invalid Routes:
1. Visit non-existent route: `https://yoursite.com/flights/invalid-route`
2. **Expected Result**:
   - Should show normal page content (fallback to standard page rendering)
   - No flight-specific content
   - No errors or broken page

### 4. Test Content Element Rendering

#### Test Standard Content Elements:
1. Add various content elements to the Flight Landing Page:
   - Text & Media
   - Images
   - Lists
   - Tables
2. Visit both normal page and virtual routes
3. **Expected Result**:
   - All content elements render correctly
   - No conflicts between flight functionality and standard content
   - Content is properly styled

#### Test Template Page Content:
1. Add content elements to the Flight Template Page with placeholders:
   - **Header**: "Book Your Flight from {origin} to {destination}"
   - **Text**: "Price starting from {price} {currency}"
   - **Text**: "Flight time: {flight.duration}"
2. Visit virtual routes
3. **Expected Result**:
   - Template content is rendered with placeholders replaced
   - Content maintains proper formatting and styling
   - No raw placeholder text is visible

### 5. Test Site Integration

#### Test Navigation:
1. Add the Flight Landing Page to your site's main navigation
2. Visit the site homepage
3. Click on the flight page link in navigation
4. **Expected Result**:
   - Navigation link works correctly
   - Page is properly highlighted as active in navigation
   - Breadcrumb navigation works (if enabled)

#### Test SEO and Meta Tags:
1. Visit virtual routes and check page source
2. **Expected Result**:
   - Page title reflects the route (e.g., "Flights from Berlin to Sofia")
   - Meta description is appropriate
   - Canonical URLs are correct
   - No duplicate content issues

#### Test Multi-language (if applicable):
1. If your site has multiple languages, test:
   - Language switching works on flight pages
   - Virtual routes work in all languages
   - Content is properly translated

### 6. Test Error Handling

#### Test Missing Template Page:
1. Edit the Flight Landing Page
2. Remove the template page reference
3. Visit virtual routes
4. **Expected Result**:
   - Page should fallback to normal page rendering
   - No errors or broken content
   - Standard page content is still visible

#### Test Hidden Template Page:
1. Hide the template page
2. Visit virtual routes
3. **Expected Result**:
   - Graceful fallback to normal page rendering
   - No errors displayed to users

#### Test Inactive Routes:
1. Deactivate a flight route
2. Visit the deactivated route URL
3. **Expected Result**:
   - Should fallback to normal page rendering
   - No flight-specific content shown
   - No errors

### 7. Performance Testing

#### Test Page Load Speed:
1. Use browser developer tools to check:
   - Page load time for normal pages
   - Page load time for virtual routes
   - Number of database queries
   - Memory usage

#### Test Caching:
1. Visit virtual routes multiple times
2. Check that:
   - Pages load quickly on subsequent visits
   - Content is properly cached
   - Cache invalidation works when routes are updated

## Expected Behavior Summary

### Normal Page Access (`/flights`):
- ✅ Standard TYPO3 page rendering
- ✅ Normal content elements displayed
- ✅ Navigation and site structure intact
- ✅ No flight-specific content
- ✅ Proper HTML structure and meta tags

### Virtual Route Access (`/flights/ber-sof`):
- ✅ Dynamic page title based on route
- ✅ Template page content with replaced placeholders
- ✅ Route information displayed
- ✅ Standard page structure maintained
- ✅ Navigation and footer still work

### Error Scenarios:
- ✅ Invalid routes fallback to normal page
- ✅ Missing template pages don't break the page
- ✅ Inactive routes are handled gracefully
- ✅ No PHP errors or exceptions

### Integration:
- ✅ Works with TYPO3 navigation
- ✅ Proper SEO and meta tags
- ✅ Multi-language support (if applicable)
- ✅ Caching works correctly
- ✅ Performance is acceptable

## Troubleshooting

### Page Shows 404 Error:
- Check that the Flight Landing Page is not hidden
- Verify the page slug is correct
- Ensure the page is published and accessible

### Virtual Routes Don't Work:
- Check that flight routes are active
- Verify the route slugs are correct
- Ensure the FlightRouteProcessor is properly configured

### Placeholders Not Replaced:
- Check that the template page is properly referenced
- Verify the PlaceholderService is working
- Ensure flight data is being generated correctly

### Content Not Rendering:
- Check TypoScript configuration
- Verify lib.dynamicContent is properly configured
- Ensure content elements are not hidden

## Technical Implementation

The frontend rendering is implemented through:

1. **TypoScript Configuration**: `Configuration/TypoScript/setup.typoscript`
   - Page type conditions for doktype 201
   - FluidTemplate configuration
   - Content rendering setup

2. **Data Processor**: `Classes/DataProcessing/FlightRouteProcessor.php`
   - Detects virtual routes from URL
   - Loads flight route data
   - Processes template page content

3. **Page Template**: `Resources/Private/Templates/Page/Default.html`
   - Handles both normal and virtual route rendering
   - Displays dynamic content based on route data

4. **Content Rendering**: `lib.dynamicContent` TypoScript object
   - Renders standard TYPO3 content elements
   - Maintains compatibility with all content types
