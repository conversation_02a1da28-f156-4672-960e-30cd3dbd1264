<?php
namespace Bgs\FlightLandingPages\DataProcessing;

use TYPO3\CMS\Frontend\ContentObject\ContentObjectRenderer;
use TYPO3\CMS\Frontend\ContentObject\DataProcessorInterface;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use Bgs\FlightLandingPages\Domain\Repository\FlightRouteRepository;
use Bgs\FlightLandingPages\Service\PlaceholderService;

/**
 * Data processor for Flight Landing Pages
 *
 * Provides flight route data and template page content to the page template
 */
class FlightRouteProcessor implements DataProcessorInterface
{
    protected FlightRouteRepository $flightRouteRepository;
    protected PlaceholderService $placeholderService;

    public function __construct(
        FlightRouteRepository $flightRouteRepository = null,
        PlaceholderService $placeholderService = null
    ) {
        $this->flightRouteRepository = $flightRouteRepository ?? GeneralUtility::makeInstance(FlightRouteRepository::class);
        $this->placeholderService = $placeholderService ?? GeneralUtility::makeInstance(PlaceholderService::class);
    }

    /**
     * Process data for Flight Landing Pages
     */
    public function process(
        ContentObjectRenderer $cObj,
        array $contentObjectConfiguration,
        array $processorConfiguration,
        array $processedData
    ): array {
        $targetVariableName = $cObj->stdWrapValue('as', $processorConfiguration, 'flightRouteData');

        // Get current page data
        $pageData = $processedData['data'] ?? [];
        $pageUid = (int)($pageData['uid'] ?? 0);
        $doktype = (int)($pageData['doktype'] ?? 0);

        // Only process for Flight Landing Pages (doktype 201)
        if ($doktype !== 201) {
            $processedData[$targetVariableName] = [];
            return $processedData;
        }

        // Get template page UID
        $templatePageUid = (int)($pageData['tx_flightlandingpages_template_page'] ?? 0);

        // Get flight routes for this landing page
        $flightRoutes = [];
        if ($pageUid > 0) {
            $flightRoutesObjects = $this->flightRouteRepository->findByLandingPage($pageUid);
            // Convert objects to arrays for easier template usage
            foreach ($flightRoutesObjects as $route) {
                $flightRoutes[] = [
                    'uid' => $route->getUid(),
                    'originCode' => $route->getOriginCode(),
                    'originName' => $route->getOriginName(),
                    'originType' => $route->getOriginType(),
                    'destinationCode' => $route->getDestinationCode(),
                    'destinationName' => $route->getDestinationName(),
                    'destinationType' => $route->getDestinationType(),
                    'routeSlug' => $route->getRouteSlug(),
                    'isActive' => $route->getIsActive(),
                    'landingPage' => $route->getLandingPage()
                ];
            }
        }

        // Get template page content if available
        $templatePageContent = [];
        if ($templatePageUid > 0) {
            $templatePageContent = $this->getTemplatePageContent($templatePageUid);
        }

        // Check if this is a virtual route (URL contains route parameters)
        $routeData = $this->extractRouteFromUrl();
        $isVirtualRoute = !empty($routeData);

        // If it's a virtual route, find the matching flight route
        $currentFlightRoute = null;
        if ($isVirtualRoute && !empty($flightRoutes)) {
            $currentFlightRoute = $this->findMatchingRoute($flightRoutes, $routeData);
        }

        // Prepare flight data for placeholder replacement
        $flightData = [];
        if ($currentFlightRoute) {
            $flightData = $this->buildFlightData($currentFlightRoute);
        }

        // Process template content with placeholders
        $processedTemplateContent = [];
        if (!empty($templatePageContent) && !empty($flightData)) {
            foreach ($templatePageContent as $contentElement) {
                $processedContent = $contentElement;
                if (isset($contentElement['bodytext'])) {
                    $processedContent['bodytext'] = $this->placeholderService->replacePlaceholders(
                        $contentElement['bodytext'],
                        $flightData
                    );
                }
                if (isset($contentElement['header'])) {
                    $processedContent['header'] = $this->placeholderService->replacePlaceholders(
                        $contentElement['header'],
                        $flightData
                    );
                }
                $processedTemplateContent[] = $processedContent;
            }
        }

        // Assign data to template
        $processedData[$targetVariableName] = [
            'isVirtualRoute' => $isVirtualRoute,
            'currentFlightRoute' => $currentFlightRoute,
            'allFlightRoutes' => $flightRoutes,
            'templatePageContent' => $processedTemplateContent,
            'flightData' => $flightData,
            'routeData' => $routeData,
            'templatePageUid' => $templatePageUid,
            'landingPageUid' => $pageUid
        ];

        return $processedData;
    }

    /**
     * Extract route parameters from current URL
     */
    protected function extractRouteFromUrl(): array
    {
        $request = $GLOBALS['TYPO3_REQUEST'] ?? null;
        if (!$request) {
            return [];
        }

        $uri = $request->getUri();
        $path = trim($uri->getPath(), '/');
        $pathParts = explode('/', $path);

        // Look for route pattern like "ber-sof" in the URL
        $routeSlug = end($pathParts);
        if (preg_match('/^([a-z]{3})-([a-z]{3})$/i', $routeSlug, $matches)) {
            return [
                'originCode' => strtoupper($matches[1]),
                'destinationCode' => strtoupper($matches[2]),
                'routeSlug' => $routeSlug
            ];
        }

        return [];
    }

    /**
     * Find matching flight route based on route data
     */
    protected function findMatchingRoute(array $flightRoutes, array $routeData): ?array
    {
        foreach ($flightRoutes as $route) {
            if (
                $route['originCode'] === $routeData['originCode'] &&
                $route['destinationCode'] === $routeData['destinationCode']
            ) {
                return $route;
            }
        }
        return null;
    }

    /**
     * Build flight data array for placeholder replacement
     */
    protected function buildFlightData(array $flightRoute): array
    {
        return [
            'origin' => $flightRoute['originName'] ?? $flightRoute['originCode'],
            'destination' => $flightRoute['destinationName'] ?? $flightRoute['destinationCode'],
            'originCode' => $flightRoute['originCode'],
            'destinationCode' => $flightRoute['destinationCode'],
            'originType' => $flightRoute['originType'],
            'destinationType' => $flightRoute['destinationType'],
            'routeSlug' => $flightRoute['routeSlug'],
            'price' => '299', // Mock data - would come from API
            'currency' => 'EUR',
            'airline' => 'Example Airlines',
            'flight' => [
                'number' => 'EA123',
                'duration' => '2h 30m',
                'departureTime' => '10:30',
                'arrivalTime' => '13:00',
            ]
        ];
    }

    /**
     * Get template page content elements
     */
    protected function getTemplatePageContent(int $templatePageUid): array
    {
        $queryBuilder = GeneralUtility::makeInstance(\TYPO3\CMS\Core\Database\ConnectionPool::class)
            ->getQueryBuilderForTable('tt_content');

        $result = $queryBuilder
            ->select('*')
            ->from('tt_content')
            ->where(
                $queryBuilder->expr()->eq('pid', $queryBuilder->createNamedParameter($templatePageUid, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('hidden', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('deleted', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT))
            )
            ->orderBy('sorting')
            ->executeQuery();

        return $result->fetchAllAssociative();
    }
}
