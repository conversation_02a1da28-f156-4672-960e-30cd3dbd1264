<?php
namespace Bgs\FlightLandingPages\Domain\Model;

use TYPO3\CMS\Extbase\DomainObject\AbstractEntity;

/**
 * Landing Page Model
 *
 * Represents a landing page (doktype=201) that defines URL patterns
 * and references template pages for generating dynamic content.
 */
class LandingPage extends AbstractEntity
{
    protected string $title = '';
    protected string $slug = '';
    protected string $description = '';
    protected bool $hidden = false;
    protected int $starttime = 0;
    protected int $endtime = 0;
    protected int $doktype = 201;

    // Flight landing pages specific fields
    protected int $templatePage = 0;
    protected string $urlPattern = '';
    protected string $routePrefix = '';
    protected int $cacheLifetime = 3600;
    protected bool $enableSitemap = false;

    // Getters and setters
    public function getTitle(): string
    {
        return $this->title;
    }

    public function setTitle(string $title): void
    {
        $this->title = $title;
    }

    public function getSlug(): string
    {
        return $this->slug;
    }

    public function setSlug(string $slug): void
    {
        $this->slug = $slug;
    }

    public function getDescription(): string
    {
        return $this->description;
    }

    public function setDescription(string $description): void
    {
        $this->description = $description;
    }

    public function getHidden(): bool
    {
        return $this->hidden;
    }

    public function setHidden(bool $hidden): void
    {
        $this->hidden = $hidden;
    }

    public function getStarttime(): int
    {
        return $this->starttime;
    }

    public function setStarttime(int $starttime): void
    {
        $this->starttime = $starttime;
    }

    public function getEndtime(): int
    {
        return $this->endtime;
    }

    public function setEndtime(int $endtime): void
    {
        $this->endtime = $endtime;
    }

    public function getDoktype(): int
    {
        return $this->doktype;
    }

    public function setDoktype(int $doktype): void
    {
        $this->doktype = $doktype;
    }

    public function getTemplatePage(): int
    {
        return $this->templatePage;
    }

    public function setTemplatePage(int $templatePage): void
    {
        $this->templatePage = $templatePage;
    }

    public function getUrlPattern(): string
    {
        return $this->urlPattern;
    }

    public function setUrlPattern(string $urlPattern): void
    {
        $this->urlPattern = $urlPattern;
    }

    public function getRoutePrefix(): string
    {
        return $this->routePrefix;
    }

    public function setRoutePrefix(string $routePrefix): void
    {
        $this->routePrefix = $routePrefix;
    }

    public function getCacheLifetime(): int
    {
        return $this->cacheLifetime;
    }

    public function setCacheLifetime(int $cacheLifetime): void
    {
        $this->cacheLifetime = $cacheLifetime;
    }

    public function getEnableSitemap(): bool
    {
        return $this->enableSitemap;
    }

    public function setEnableSitemap(bool $enableSitemap): void
    {
        $this->enableSitemap = $enableSitemap;
    }

    /**
     * Check if the landing page is accessible (not hidden and within time constraints)
     */
    public function isAccessible(): bool
    {
        if ($this->hidden) {
            return false;
        }

        $now = time();

        if ($this->starttime > 0 && $now < $this->starttime) {
            return false;
        }

        if ($this->endtime > 0 && $now > $this->endtime) {
            return false;
        }

        return true;
    }

    /**
     * Extract placeholder names from URL pattern
     *
     * @return array Array of placeholder names (e.g., ['origin', 'destination'])
     */
    public function getPlaceholderNames(): array
    {
        preg_match_all('/\{([^}]+)\}/', $this->urlPattern, $matches);
        return $matches[1] ?? [];
    }

    /**
     * Check if URL pattern matches a given path and extract parameters
     *
     * @param string $path The URL path to match
     * @return array|null Array of extracted parameters or null if no match
     */
    public function matchUrlPattern(string $path): ?array
    {
        // Convert URL pattern to regex
        $pattern = preg_replace('/\{([^}]+)\}/', '([^/]+)', $this->urlPattern);
        $pattern = '#^' . $pattern . '$#';

        if (preg_match($pattern, $path, $matches)) {
            array_shift($matches); // Remove full match
            $placeholders = $this->getPlaceholderNames();

            return array_combine($placeholders, $matches);
        }

        return null;
    }

    /**
     * Generate URL from parameters
     *
     * @param array $parameters Associative array of parameters
     * @return string Generated URL
     */
    public function generateUrl(array $parameters): string
    {
        $url = $this->urlPattern;

        foreach ($parameters as $key => $value) {
            $url = str_replace('{' . $key . '}', $value, $url);
        }

        return $url;
    }

    /**
     * Generate full URL with site endpoint, landing page path, and pair slug
     *
     * @param string $siteBaseUrl Site base URL
     * @param string $pairSlug Pair slug (e.g., "from-BER-to-SOF")
     * @return string Full URL in format: Site Endpoint + Landing Page Path + Pair Slug
     */
    public function generateFullUrl(string $siteBaseUrl, string $pairSlug): string
    {
        // Ensure base URL ends with /
        $baseUrl = rtrim($siteBaseUrl, '/') . '/';

        // Get landing page path from slug
        $landingPagePath = ltrim($this->slug, '/');

        // Combine: Site Endpoint + Landing Page Path + Pair Slug
        $fullPath = $landingPagePath . '/' . ltrim($pairSlug, '/');

        return $baseUrl . ltrim($fullPath, '/');
    }
}
