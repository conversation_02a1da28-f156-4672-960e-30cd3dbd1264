<?php
namespace Bgs\FlightLandingPages\Domain\Model;

use TYPO3\CMS\Extbase\DomainObject\AbstractEntity;

/**
 * Template Page Model
 * 
 * Represents a template page (doktype=200) that contains content elements
 * with placeholders that can be used to generate dynamic landing pages.
 */
class TemplatePage extends AbstractEntity
{
    protected string $title = '';
    protected string $slug = '';
    protected string $navTitle = '';
    protected string $description = '';
    protected bool $hidden = false;
    protected bool $navHide = false;
    protected int $starttime = 0;
    protected int $endtime = 0;
    protected int $doktype = 200;

    // Getters and setters
    public function getTitle(): string 
    { 
        return $this->title; 
    }
    
    public function setTitle(string $title): void 
    { 
        $this->title = $title; 
    }

    public function getSlug(): string 
    { 
        return $this->slug; 
    }
    
    public function setSlug(string $slug): void 
    { 
        $this->slug = $slug; 
    }

    public function getNavTitle(): string 
    { 
        return $this->navTitle; 
    }
    
    public function setNavTitle(string $navTitle): void 
    { 
        $this->navTitle = $navTitle; 
    }

    public function getDescription(): string 
    { 
        return $this->description; 
    }
    
    public function setDescription(string $description): void 
    { 
        $this->description = $description; 
    }

    public function getHidden(): bool 
    { 
        return $this->hidden; 
    }
    
    public function setHidden(bool $hidden): void 
    { 
        $this->hidden = $hidden; 
    }

    public function getNavHide(): bool 
    { 
        return $this->navHide; 
    }
    
    public function setNavHide(bool $navHide): void 
    { 
        $this->navHide = $navHide; 
    }

    public function getStarttime(): int 
    { 
        return $this->starttime; 
    }
    
    public function setStarttime(int $starttime): void 
    { 
        $this->starttime = $starttime; 
    }

    public function getEndtime(): int 
    { 
        return $this->endtime; 
    }
    
    public function setEndtime(int $endtime): void 
    { 
        $this->endtime = $endtime; 
    }

    public function getDoktype(): int 
    { 
        return $this->doktype; 
    }
    
    public function setDoktype(int $doktype): void 
    { 
        $this->doktype = $doktype; 
    }

    /**
     * Check if the template page is accessible (not hidden and within time constraints)
     */
    public function isAccessible(): bool
    {
        if ($this->hidden) {
            return false;
        }

        $now = time();
        
        if ($this->starttime > 0 && $now < $this->starttime) {
            return false;
        }
        
        if ($this->endtime > 0 && $now > $this->endtime) {
            return false;
        }

        return true;
    }

    /**
     * Get the display title (nav_title if set, otherwise title)
     */
    public function getDisplayTitle(): string
    {
        return $this->navTitle ?: $this->title;
    }
}
