<?php
namespace Bgs\FlightLandingPages\ViewHelpers;

use TYPO3<PERSON>luid\Fluid\Core\Rendering\RenderingContextInterface;
use TYPO3Fluid\Fluid\Core\ViewHelper\AbstractViewHelper;
use TYPO3Fluid\Fluid\Core\ViewHelper\Traits\CompileWithRenderStatic;
use Bgs\FlightLandingPages\Service\PlaceholderService;

class ReplacePlaceholderViewHelper extends AbstractViewHelper 
{
    use CompileWithRenderStatic;

    protected $escapeOutput = false;

    public function initializeArguments() 
    {
        $this->registerArgument('content', 'string', 'Content with placeholders', true);
        $this->registerArgument('data', 'array', 'Data to replace placeholders', true);
    }

    public static function renderStatic(
        array $arguments,
        \Closure $renderChildrenClosure,
        RenderingContextInterface $renderingContext
    ) {
        $placeholderService = \TYPO3\CMS\Core\Utility\GeneralUtility::makeInstance(PlaceholderService::class);
        return $placeholderService->replacePlaceholders($arguments['content'], $arguments['data']);
    }
}
