<?php
namespace Bgs\FlightLandingPages\ViewHelpers;

use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Fluid\Core\ViewHelper\AbstractViewHelper;
use TYPO3\CMS\Fluid\Core\Rendering\RenderingContextInterface;
use TYPO3\CMS\Fluid\Core\ViewHelper\Traits\CompileWithRenderStatic;
use Bgs\FlightLandingPages\Service\PlaceholderService;

/**
 * ViewHelper to render flight-specific content in site templates
 * 
 * Usage in site templates:
 * <flp:flightContent>
 *   <f:if condition="{flightRouteData.isVirtualRoute}">
 *     <h1>Flights from {flightRouteData.currentFlightRoute.originName} to {flightRouteData.currentFlightRoute.destinationName}</h1>
 *   </f:if>
 * </flp:flightContent>
 */
class FlightContentViewHelper extends AbstractViewHelper
{
    use CompileWithRenderStatic;

    protected $escapeOutput = false;

    public function initializeArguments()
    {
        $this->registerArgument('flightData', 'array', 'Flight route data from data processor', false, []);
        $this->registerArgument('templateContent', 'string', 'Template content with placeholders', false, '');
    }

    public static function renderStatic(
        array $arguments,
        \Closure $renderChildrenClosure,
        RenderingContextInterface $renderingContext
    ) {
        $templateVariableContainer = $renderingContext->getVariableProvider();
        
        // Get flight route data from template variables (set by data processor)
        $flightRouteData = $arguments['flightData'] ?: $templateVariableContainer->get('flightRouteData');
        
        if (empty($flightRouteData) || !is_array($flightRouteData)) {
            // No flight data available, render children normally
            return $renderChildrenClosure();
        }

        // Add flight route data to template variables for child content
        $templateVariableContainer->add('flightRouteData', $flightRouteData);
        
        // Process template content if provided
        $templateContent = $arguments['templateContent'];
        if (!empty($templateContent) && !empty($flightRouteData['flightData'])) {
            $placeholderService = GeneralUtility::makeInstance(PlaceholderService::class);
            $processedContent = $placeholderService->replacePlaceholders($templateContent, $flightRouteData['flightData']);
            $templateVariableContainer->add('processedTemplateContent', $processedContent);
        }

        // Render children with flight data available
        $content = $renderChildrenClosure();
        
        // Clean up template variables
        if ($templateVariableContainer->exists('flightRouteData')) {
            $templateVariableContainer->remove('flightRouteData');
        }
        if ($templateVariableContainer->exists('processedTemplateContent')) {
            $templateVariableContainer->remove('processedTemplateContent');
        }
        
        return $content;
    }
}
