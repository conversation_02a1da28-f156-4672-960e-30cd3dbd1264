<?php
namespace Bgs\FlightLandingPages\Controller;

use Psr\Http\Message\ResponseInterface;
use TYPO3\CMS\Core\Site\Entity\Site;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Extbase\Mvc\Controller\ActionController;

use Bgs\FlightLandingPages\Domain\Repository\FlightRouteRepository;
use Bgs\FlightLandingPages\Service\PlaceholderService;

class FlightPageController extends ActionController
{
    protected PlaceholderService $placeholderService;
    protected FlightRouteRepository $flightRouteRepository;

    public function __construct(
        PlaceholderService $placeholderService,
        FlightRouteRepository $flightRouteRepository
    ) {
        $this->placeholderService = $placeholderService;
        $this->flightRouteRepository = $flightRouteRepository;
    }

    /**
     * Show a flight landing page with dynamic content
     */
    public function showAction(): ResponseInterface
    {
        // Regular flight page display
        $flightId = $this->settings['flightId'] ?? '';
        $flightData = $this->getFlightData($flightId);

        // Get template content
        $templateFile = $this->settings['templateFile'] ?? $this->settings['defaultTemplate'];
        $templateContent = $this->getTemplateContent($templateFile);

        // Process template with placeholders
        $processedContent = $this->placeholderService->replacePlaceholders($templateContent, $flightData);

        $this->view->assignMultiple([
            'flightData' => $flightData,
            'processedContent' => $processedContent
        ]);

        return $this->htmlResponse();
    }



    /**
     * Get template content from file
     */
    protected function getTemplateContent(string $templateFile): string
    {
        if (!$templateFile) {
            return '';
        }

        $templatePath = GeneralUtility::getFileAbsFileName($templateFile);
        if (file_exists($templatePath)) {
            return file_get_contents($templatePath);
        }

        return '';
    }

    /**
     * Get flight data from API or cache (existing method)
     */
    protected function getFlightData(string $flightId): array
    {
        // Mock data for example - in real implementation this would fetch from API
        return [
            'id' => $flightId,
            'origin' => 'Berlin',
            'destination' => 'New York',
            'departureDate' => '2023-12-15',
            'returnDate' => '2023-12-22',
            'price' => '499',
            'currency' => 'EUR',
            'airline' => 'Example Airlines',
            'flight' => [
                'number' => 'EA123',
                'duration' => '8h 30m',
                'departureTime' => '10:30',
                'arrivalTime' => '13:00',
            ],
        ];
    }
}
