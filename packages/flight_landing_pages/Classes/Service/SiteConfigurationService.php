<?php
namespace Bgs\FlightLandingPages\Service;

use TYPO3\CMS\Core\Site\Entity\Site;
use TYPO3\CMS\Core\Site\SiteFinder;
use TYPO3\CMS\Core\Utility\GeneralUtility;

class SiteConfigurationService
{
    protected SiteFinder $siteFinder;

    public function __construct(SiteFinder $siteFinder)
    {
        $this->siteFinder = $siteFinder;
    }

    /**
     * Get flight landing pages configuration for a site
     */
    public function getFlightConfiguration(string $siteIdentifier): array
    {
        try {
            $site = $this->siteFinder->getSiteByIdentifier($siteIdentifier);
            $configuration = $site->getConfiguration();

            return $configuration['flightLandingPages'] ?? [];
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Get all sites with flight landing pages configuration
     */
    public function getSitesWithFlightConfiguration(): array
    {
        $sites = [];
        foreach ($this->siteFinder->getAllSites() as $site) {
            $config = $this->getFlightConfiguration($site->getIdentifier());
            if (!empty($config)) {
                $sites[] = $site;
            }
        }
        return $sites;
    }


}
