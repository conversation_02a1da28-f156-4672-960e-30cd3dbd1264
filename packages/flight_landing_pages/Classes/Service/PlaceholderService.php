<?php
namespace Bgs\FlightLandingPages\Service;

class PlaceholderService 
{
    /**
     * Replace placeholders in template with actual flight data
     *
     * @param string $template Template with placeholders
     * @param array $flightData Flight data to replace placeholders
     * @return string Processed template
     */
    public function replacePlaceholders(string $template, array $flightData): string 
    {
        // Replace placeholders like {flight.destination}, {flight.price}, etc.
        foreach ($flightData as $key => $value) {
            if (is_array($value)) {
                foreach ($value as $subKey => $subValue) {
                    $template = str_replace('{' . $key . '.' . $subKey . '}', $subValue, $template);
                }
            } else {
                $template = str_replace('{' . $key . '}', $value, $template);
            }
        }

        return $template;
    }
}
