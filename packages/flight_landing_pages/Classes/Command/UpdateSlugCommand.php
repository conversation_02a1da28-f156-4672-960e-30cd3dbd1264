<?php

declare(strict_types=1);

namespace Bgs\FlightLandingPages\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Utility\GeneralUtility;

/**
 * Command to update existing flight route slugs to the new format
 */
class UpdateSlugCommand extends Command
{
    protected function configure(): void
    {
        $this->setDescription('Update existing flight route slugs to the new "from-X-to-Y" format');
        $this->setHelp('This command updates all existing flight route records to use the new slug format.');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $io->title('Flight Route Slug Update');

        $connection = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getConnectionForTable('tx_flightlandingpages_domain_model_flightroute');

        // Get all flight route records
        $queryBuilder = $connection->createQueryBuilder();
        $records = $queryBuilder
            ->select('uid', 'pid', 'origin_code', 'destination_code', 'route_slug')
            ->from('tx_flightlandingpages_domain_model_flightroute')
            ->where($queryBuilder->expr()->neq('deleted', 1))
            ->executeQuery()
            ->fetchAllAssociative();

        if (empty($records)) {
            $io->success('No flight route records found.');
            return Command::SUCCESS;
        }

        $io->note(sprintf('Found %d flight route records to update.', count($records)));

        $updated = 0;
        $skipped = 0;

        foreach ($records as $record) {
            $originCode = strtoupper(trim($record['origin_code']));
            $destinationCode = strtoupper(trim($record['destination_code']));
            $currentSlug = $record['route_slug'];
            $pid = (int)$record['pid'];

            // Get parent page path
            $parentPagePath = $this->getParentPagePath($pid);

            // Generate new slug format with parent path
            $baseSlug = strtolower($originCode) . '-' . strtolower($destinationCode);
            $newSlug = !empty($parentPagePath) ? ltrim($parentPagePath, '/') . '/' . $baseSlug : $baseSlug;

            // Skip if already in correct format
            if ($currentSlug === $newSlug) {
                $skipped++;
                continue;
            }

            // Update the record using direct SQL to avoid parameter issues
            $connection->executeStatement(
                'UPDATE tx_flightlandingpages_domain_model_flightroute SET route_slug = ? WHERE uid = ?',
                [$newSlug, $record['uid']]
            );

            $io->writeln(sprintf(
                'Updated UID %d: "%s" → "%s"',
                $record['uid'],
                $currentSlug,
                $newSlug
            ));

            $updated++;
        }

        $io->success(sprintf(
            'Update completed! Updated: %d, Skipped: %d, Total: %d',
            $updated,
            $skipped,
            count($records)
        ));

        return Command::SUCCESS;
    }

    /**
     * Get the full path of the parent page
     *
     * @param int $pid Page ID
     * @return string Parent page path
     */
    protected function getParentPagePath(int $pid): string
    {
        if ($pid <= 0) {
            return '';
        }

        try {
            $connection = GeneralUtility::makeInstance(ConnectionPool::class)
                ->getConnectionForTable('pages');

            $queryBuilder = $connection->createQueryBuilder();
            $page = $queryBuilder
                ->select('uid', 'pid', 'slug', 'title')
                ->from('pages')
                ->where(
                    $queryBuilder->expr()->eq('uid', $queryBuilder->createNamedParameter($pid, \PDO::PARAM_INT)),
                    $queryBuilder->expr()->eq('deleted', 0)
                )
                ->executeQuery()
                ->fetchAssociative();

            if (!$page) {
                return '';
            }

            // Use the page's slug if available
            if (!empty($page['slug'])) {
                return $page['slug'];
            }

            // Fallback: build path from title if no slug
            return '/' . $this->sanitizeSlug($page['title']);

        } catch (\Exception $e) {
            return '';
        }
    }

    /**
     * Simple slug sanitization for fallback cases
     *
     * @param string $value
     * @return string
     */
    protected function sanitizeSlug(string $value): string
    {
        // Basic sanitization - convert to lowercase, replace spaces and special chars with dashes
        $value = strtolower(trim($value));
        $value = preg_replace('/[^a-z0-9\-]/', '-', $value);
        $value = preg_replace('/-+/', '-', $value);
        return trim($value, '-');
    }
}
