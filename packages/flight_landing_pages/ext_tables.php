<?php
defined('TYPO3') or die();

call_user_func(static function () {
    // Register backend module if needed
    \TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addLLrefForTCAdescr(
        'tx_flightlandingpages_domain_model_flightroute',
        'EXT:flight_landing_pages/Resources/Private/Language/locallang_csh_tx_flightlandingpages_domain_model_flightroute.xlf'
    );
    \TYPO3\CMS\Core\Utility\ExtensionManagementUtility::allowTableOnStandardPages('tx_flightlandingpages_domain_model_flightroute');
});
