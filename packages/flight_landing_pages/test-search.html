<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flight Search Test</title>
    <style>
        /* Search Filter Styles */
        .flight-search-filter {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
        }

        .search-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .search-group label {
            font-weight: bold;
            color: #2c3e50;
            font-size: 1.1em;
        }

        .search-input-wrapper {
            position: relative;
            display: flex;
            align-items: center;
        }

        .route-search-input {
            padding: 12px 16px;
            padding-right: 40px;
            border: 2px solid #bdc3c7;
            border-radius: 6px;
            font-size: 1em;
            transition: border-color 0.3s ease;
            background: white;
            width: 100%;
        }

        .route-search-input:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .clear-search-btn {
            position: absolute;
            right: 8px;
            background: none;
            border: none;
            font-size: 20px;
            color: #7f8c8d;
            cursor: pointer;
            padding: 4px 8px;
            border-radius: 50%;
            transition: background-color 0.2s ease, color 0.2s ease;
            line-height: 1;
        }

        .clear-search-btn:hover {
            background-color: #e9ecef;
            color: #495057;
        }

        .results-count {
            color: #7f8c8d;
            font-size: 0.9em;
            font-style: italic;
        }

        /* Route Items */
        .route-item {
            background: white;
            border: 1px solid #e1e8ed;
            border-radius: 6px;
            padding: 15px 20px;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: background-color 0.2s;
        }

        .route-item:hover {
            background-color: #f8f9fa;
        }

        .route-name {
            font-weight: bold;
            color: #2c3e50;
        }

        .route-codes {
            color: #7f8c8d;
            font-size: 0.9em;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="flight-reference-list">
        <h2>Available Flight Routes - Test</h2>

        <!-- Search Filter -->
        <div class="flight-search-filter">
            <div class="search-group">
                <label for="route-search">Search Routes:</label>
                <div class="search-input-wrapper">
                    <input type="text" id="route-search" class="route-search-input" placeholder="Search by airport codes, city names, or route slugs...">
                    <button type="button" id="clear-search" class="clear-search-btn" title="Clear search" style="display: none;">×</button>
                </div>
                <div class="search-results-info">
                    <span class="results-count"></span>
                </div>
            </div>
        </div>

        <!-- Test Routes -->
        <div class="flight-routes-list">
            <ul class="route-list" style="list-style: none; padding: 0;">
                <li class="route-item" 
                    data-origin="BER" 
                    data-destination="SOF"
                    data-search-content="BER SOF Berlin Sofia ber-sof">
                    <div class="route-info">
                        <span class="route-name">Berlin → Sofia</span>
                        <span class="route-codes">(BER → SOF)</span>
                    </div>
                    <div class="route-info">
                        <span class="route-info-text">Flight route available</span>
                    </div>
                </li>
                <li class="route-item" 
                    data-origin="BER" 
                    data-destination="VIE"
                    data-search-content="BER VIE Berlin Vienna ber-vie">
                    <div class="route-info">
                        <span class="route-name">Berlin → Vienna</span>
                        <span class="route-codes">(BER → VIE)</span>
                    </div>
                    <div class="route-info">
                        <span class="route-info-text">Flight route available</span>
                    </div>
                </li>
                <li class="route-item" 
                    data-origin="MUC" 
                    data-destination="SOF"
                    data-search-content="MUC SOF Munich Sofia muc-sof">
                    <div class="route-info">
                        <span class="route-name">Munich → Sofia</span>
                        <span class="route-codes">(MUC → SOF)</span>
                    </div>
                    <div class="route-info">
                        <span class="route-info-text">Flight route available</span>
                    </div>
                </li>
                <li class="route-item" 
                    data-origin="FRA" 
                    data-destination="PRG"
                    data-search-content="FRA PRG Frankfurt Prague fra-prg">
                    <div class="route-info">
                        <span class="route-name">Frankfurt → Prague</span>
                        <span class="route-codes">(FRA → PRG)</span>
                    </div>
                    <div class="route-info">
                        <span class="route-info-text">Flight route available</span>
                    </div>
                </li>
            </ul>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('route-search');
            const clearButton = document.getElementById('clear-search');
            const routeItems = document.querySelectorAll('[data-origin][data-destination]');

            function filterRoutes() {
                const searchTerm = searchInput ? searchInput.value.toLowerCase().trim() : '';

                // Show/hide clear button
                if (clearButton && searchInput) {
                    clearButton.style.display = searchTerm ? 'block' : 'none';
                }

                routeItems.forEach(function(item) {
                    const searchContent = item.getAttribute('data-search-content') || '';
                    const searchMatch = !searchTerm || searchContent.toLowerCase().includes(searchTerm);

                    if (searchMatch) {
                        item.style.display = '';
                    } else {
                        item.style.display = 'none';
                    }
                });

                updateResultsCount();
            }

            function updateResultsCount() {
                const visibleItems = Array.from(routeItems).filter(item => item.style.display !== 'none');
                const countElement = document.querySelector('.results-count');

                if (countElement) {
                    if (visibleItems.length === routeItems.length) {
                        countElement.textContent = `Showing all ${routeItems.length} routes`;
                    } else {
                        countElement.textContent = `Showing ${visibleItems.length} of ${routeItems.length} routes`;
                    }
                }
            }

            function clearSearch() {
                if (searchInput) {
                    searchInput.value = '';
                    searchInput.focus();
                    filterRoutes();
                }
            }

            if (searchInput) {
                searchInput.addEventListener('input', filterRoutes);
                searchInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Escape') {
                        clearSearch();
                    }
                });
            }

            if (clearButton) {
                clearButton.addEventListener('click', clearSearch);
            }

            // Initialize
            updateResultsCount();
        });
    </script>
</body>
</html>
