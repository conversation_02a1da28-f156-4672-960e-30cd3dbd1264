{"name": "bgs/flight-landing-pages", "type": "typo3-cms-extension", "description": "TYPO3 extension for managing flight landing pages with dynamic content", "keywords": ["TYPO3", "extension", "flight", "landing pages", "virtual routes"], "license": "GPL-2.0-or-later", "require": {"typo3/cms-core": "^12.4", "typo3/cms-extbase": "^12.4", "typo3/cms-fluid": "^12.4"}, "autoload": {"psr-4": {"Bgs\\FlightLandingPages\\": "Classes/"}}, "extra": {"typo3/cms": {"extension-key": "flight_landing_pages"}}}