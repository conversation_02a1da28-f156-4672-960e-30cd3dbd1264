# Flight Landing Pages Extension Setup

# Import fluid_styled_content for content element rendering
@import 'EXT:fluid_styled_content/Configuration/TypoScript/setup.typoscript'

# Configuration for Flight Template Pages (doktype 200)
# These pages should not be accessible directly from frontend
[page["doktype"] == 200]
    page = PAGE
    page {
        typeNum = 0
        config {
            disableAllHeaderCode = 1
            additionalHeaders.10.header = HTTP/1.1 404 Not Found
        }
        10 = TEXT
        10.value = Template Page - Not Accessible
    }
[END]

# Configuration for Flight Landing Pages (doktype 201)
# Add flight data processing to existing page configuration
[page["doktype"] == 201]
    # Add flight route data processor to existing page configuration
    page.10.dataProcessing {
        # Find the next available index for data processing
        1000 = Bgs\FlightLandingPages\DataProcessing\FlightRouteProcessor
        1000 {
            as = flightRouteData
        }
    }
[END]



plugin.tx_flightlandingpages {
    view {
        templateRootPaths {
            0 = EXT:flight_landing_pages/Resources/Private/Templates/
            1 = {$plugin.tx_flightlandingpages.view.templateRootPath}
        }
        partialRootPaths {
            0 = EXT:flight_landing_pages/Resources/Private/Partials/
            1 = {$plugin.tx_flightlandingpages.view.partialRootPath}
        }
        layoutRootPaths {
            0 = EXT:flight_landing_pages/Resources/Private/Layouts/
            1 = {$plugin.tx_flightlandingpages.view.layoutRootPath}
        }
    }

    persistence {
        storagePid = {$plugin.tx_flightlandingpages.persistence.storagePid}
    }

    settings {
        # Default settings, can be overridden by FlexForm
        defaultTemplate = EXT:flight_landing_pages/Resources/Private/Templates/FlightPage/Default.html
        placeholderPrefix = {flight.
        placeholderSuffix = }

        # API configuration for flight data
        api {
            baseUrl = https://api.example.com/flights
            apiKey =
            cacheLifetime = 3600
        }


    }
}

# Include CSS and JS only for flight landing pages
[page["doktype"] == 201]
    page {
        includeCSS {
            flightlandingpages = EXT:flight_landing_pages/Resources/Public/CSS/styles.css
        }
        includeJSFooter {
            flightlandingpages = EXT:flight_landing_pages/Resources/Public/JavaScript/main.js
        }
    }
[END]
