<?php
defined('TYPO3') or die();

call_user_func(static function () {
    // Register FlightPage plugin
    \TYPO3\CMS\Extbase\Utility\ExtensionUtility::registerPlugin(
        'FlightLandingPages',
        'FlightPage',
        'LLL:EXT:flight_landing_pages/Resources/Private/Language/locallang.xlf:plugin.flight_page.title',
        'mimetypes-x-content-flight',
        'flight_landing_pages'
    );



    // Register FlightReference plugin
    \TYPO3\CMS\Extbase\Utility\ExtensionUtility::registerPlugin(
        'FlightLandingPages',
        'FlightReference',
        'LLL:EXT:flight_landing_pages/Resources/Private/Language/locallang.xlf:plugin.flight_reference.title',
        'mimetypes-x-content-list',
        'flight_landing_pages'
    );

    // Add FlexForm for FlightPage plugin
    \TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addPiFlexFormValue(
        '*',
        'FILE:EXT:flight_landing_pages/Configuration/FlexForms/FlightPage.xml',
        'flightlandingpages_flightpage'
    );



    // Add FlexForm for FlightReference plugin
    \TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addPiFlexFormValue(
        '*',
        'FILE:EXT:flight_landing_pages/Configuration/FlexForms/FlightReference.xml',
        'flightlandingpages_flightreference'
    );
});
